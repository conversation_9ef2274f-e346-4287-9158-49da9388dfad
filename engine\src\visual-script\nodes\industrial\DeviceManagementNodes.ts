/**
 * 设备管理节点集合
 * 提供设备连接、监控、控制、维护、诊断等设备管理功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 设备状态枚举
 */
export enum DeviceStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  RUNNING = 'running',
  STOPPED = 'stopped',
  ERROR = 'error',
  MAINTENANCE = 'maintenance',
  IDLE = 'idle'
}

/**
 * 设备类型枚举
 */
export enum DeviceType {
  PLC = 'plc',
  SENSOR = 'sensor',
  ACTUATOR = 'actuator',
  ROBOT = 'robot',
  MACHINE = 'machine',
  CONVEYOR = 'conveyor',
  CAMERA = 'camera',
  SCANNER = 'scanner'
}

/**
 * 连接协议枚举
 */
export enum ConnectionProtocol {
  MODBUS_TCP = 'modbus_tcp',
  MODBUS_RTU = 'modbus_rtu',
  OPC_UA = 'opc_ua',
  MQTT = 'mqtt',
  ETHERNET_IP = 'ethernet_ip',
  PROFINET = 'profinet',
  HTTP = 'http',
  WEBSOCKET = 'websocket'
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  id: string;
  name: string;
  type: DeviceType;
  model: string;
  manufacturer: string;
  serialNumber: string;
  location: string;
  status: DeviceStatus;
  protocol: ConnectionProtocol;
  address: string;
  port: number;
  lastUpdate: Date;
  properties: Record<string, any>;
  tags: string[];
  description?: string;
}

/**
 * 设备监控数据接口
 */
export interface DeviceMonitorData {
  deviceId: string;
  timestamp: Date;
  parameters: {
    name: string;
    value: any;
    unit: string;
    quality: number;
    alarm?: boolean;
    warning?: boolean;
  }[];
  status: DeviceStatus;
  uptime: number;
  errorCount: number;
  warningCount: number;
}

/**
 * 设备控制命令接口
 */
export interface DeviceControlCommand {
  deviceId: string;
  command: string;
  parameters: Record<string, any>;
  timestamp: Date;
  operator: string;
  priority: number;
  timeout: number;
}

/**
 * 设备维护记录接口
 */
export interface DeviceMaintenanceRecord {
  id: string;
  deviceId: string;
  type: 'preventive' | 'corrective' | 'emergency';
  description: string;
  technician: string;
  startTime: Date;
  endTime?: Date;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  parts: {
    partNumber: string;
    description: string;
    quantity: number;
    cost: number;
  }[];
  notes?: string;
  nextMaintenanceDate?: Date;
}

/**
 * 设备诊断结果接口
 */
export interface DeviceDiagnosticResult {
  deviceId: string;
  timestamp: Date;
  diagnosticType: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  issues: {
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    recommendation: string;
    code?: string;
  }[];
  performance: {
    metric: string;
    value: number;
    unit: string;
    threshold: number;
    status: 'normal' | 'warning' | 'critical';
  }[];
  recommendations: string[];
}

/**
 * 设备管理器
 */
class DeviceManagementManager {
  private devices: Map<string, DeviceInfo> = new Map();
  private monitorData: Map<string, DeviceMonitorData[]> = new Map();
  private maintenanceRecords: Map<string, DeviceMaintenanceRecord[]> = new Map();
  private diagnosticResults: Map<string, DeviceDiagnosticResult[]> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 注册设备
   */
  registerDevice(deviceInfo: DeviceInfo): boolean {
    try {
      this.devices.set(deviceInfo.id, deviceInfo);
      this.emit('deviceRegistered', { device: deviceInfo });
      
      Debug.log('DeviceManagementManager', `设备注册: ${deviceInfo.name} (${deviceInfo.id})`);
      return true;
    } catch (error) {
      Debug.error('DeviceManagementManager', '设备注册失败', error);
      return false;
    }
  }

  /**
   * 连接设备
   */
  async connectDevice(deviceId: string): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    try {
      // 模拟设备连接过程
      await this.simulateConnection(device);
      
      device.status = DeviceStatus.ONLINE;
      device.lastUpdate = new Date();
      
      this.emit('deviceConnected', { device });
      Debug.log('DeviceManagementManager', `设备连接成功: ${device.name}`);
      return true;
    } catch (error) {
      device.status = DeviceStatus.ERROR;
      Debug.error('DeviceManagementManager', `设备连接失败: ${device.name}`, error);
      throw error;
    }
  }

  /**
   * 断开设备连接
   */
  disconnectDevice(deviceId: string): boolean {
    const device = this.devices.get(deviceId);
    if (!device) {
      return false;
    }

    device.status = DeviceStatus.OFFLINE;
    device.lastUpdate = new Date();
    
    this.emit('deviceDisconnected', { device });
    Debug.log('DeviceManagementManager', `设备断开连接: ${device.name}`);
    return true;
  }

  /**
   * 获取设备信息
   */
  getDevice(deviceId: string): DeviceInfo | undefined {
    return this.devices.get(deviceId);
  }

  /**
   * 获取所有设备
   */
  getAllDevices(): DeviceInfo[] {
    return Array.from(this.devices.values());
  }

  /**
   * 根据状态获取设备
   */
  getDevicesByStatus(status: DeviceStatus): DeviceInfo[] {
    return Array.from(this.devices.values()).filter(device => device.status === status);
  }

  /**
   * 根据类型获取设备
   */
  getDevicesByType(type: DeviceType): DeviceInfo[] {
    return Array.from(this.devices.values()).filter(device => device.type === type);
  }

  /**
   * 更新设备状态
   */
  updateDeviceStatus(deviceId: string, status: DeviceStatus): boolean {
    const device = this.devices.get(deviceId);
    if (!device) {
      return false;
    }

    const oldStatus = device.status;
    device.status = status;
    device.lastUpdate = new Date();
    
    this.emit('deviceStatusChanged', { device, oldStatus, newStatus: status });
    Debug.log('DeviceManagementManager', `设备状态更新: ${device.name} ${oldStatus} -> ${status}`);
    return true;
  }

  /**
   * 模拟设备连接
   */
  private async simulateConnection(device: DeviceInfo): Promise<void> {
    // 模拟连接延迟
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 500));
    
    // 模拟连接失败概率
    if (Math.random() < 0.05) {
      throw new Error('连接超时');
    }
  }

  /**
   * 生成设备ID
   */
  generateDeviceId(): string {
    return 'DEV_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 5);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 调度维护
   */
  scheduleMaintenance(maintenanceData: any): any {
    const maintenanceId = this.generateDeviceId();
    const maintenance = {
      id: maintenanceId,
      ...maintenanceData,
      status: 'scheduled',
      scheduledAt: new Date()
    };

    const deviceId = maintenanceData.deviceId;
    if (!this.maintenanceRecords.has(deviceId)) {
      this.maintenanceRecords.set(deviceId, []);
    }
    this.maintenanceRecords.get(deviceId)!.push(maintenance);

    return maintenance;
  }

  /**
   * 获取下次维护日期
   */
  getNextMaintenanceDate(deviceId: string): Date {
    const nextDate = new Date();
    nextDate.setDate(nextDate.getDate() + Math.floor(Math.random() * 30) + 7);
    return nextDate;
  }

  /**
   * 开始维�?   */
  startMaintenance(deviceId: string, technician: string): any {
    const maintenanceId = this.generateDeviceId();
    const maintenance = {
      id: maintenanceId,
      deviceId,
      type: 'corrective' as const,
      description: '设备维护',
      technician,
      startTime: new Date(),
      status: 'in_progress' as const,
      parts: []
    };

    if (!this.maintenanceRecords.has(deviceId)) {
      this.maintenanceRecords.set(deviceId, []);
    }
    this.maintenanceRecords.get(deviceId)!.push(maintenance);

    return maintenance;
  }

  /**
   * 完成维护
   */
  completeMaintenance(deviceId: string, description: string): any {
    const maintenanceId = this.generateDeviceId();
    const maintenance = {
      id: maintenanceId,
      deviceId,
      type: 'corrective' as const,
      description,
      technician: 'system',
      startTime: new Date(),
      endTime: new Date(),
      status: 'completed' as const,
      parts: []
    };

    if (!this.maintenanceRecords.has(deviceId)) {
      this.maintenanceRecords.set(deviceId, []);
    }
    this.maintenanceRecords.get(deviceId)!.push(maintenance);

    return maintenance;
  }

  /**
   * 取消维护
   */
  cancelMaintenance(deviceId: string): boolean {
    const records = this.maintenanceRecords.get(deviceId);
    if (records) {
      const scheduledRecord = records.find(r => r.status === 'scheduled');
      if (scheduledRecord) {
        scheduledRecord.status = 'cancelled';
        return true;
      }
    }
    return false;
  }

  /**
   * 运行诊断
   */
  runDiagnostic(deviceId: string, diagnosticType: string, _parameters: any, _threshold: any): any {
    const healthScore = Math.random() * 100;
    const status = healthScore >= 80 ? 'healthy' : healthScore >= 60 ? 'warning' : 'critical';

    const diagnostic = {
      deviceId,
      timestamp: new Date(),
      diagnosticType,
      status: status as 'healthy' | 'warning' | 'critical',
      issues: healthScore < 70 ? [
        {
          severity: 'medium' as const,
          description: '性能下降',
          recommendation: '检查冷却系统'
        },
        {
          severity: 'high' as const,
          description: '温度过高',
          recommendation: '清理设备'
        }
      ] : [],
      performance: [
        {
          metric: '健康度',
          value: healthScore,
          unit: '%',
          threshold: 70,
          status: healthScore >= 70 ? 'normal' as const : 'warning' as const
        }
      ],
      recommendations: healthScore < 70 ? ['检查冷却系统', '清理设备'] : []
    };

    if (!this.diagnosticResults.has(deviceId)) {
      this.diagnosticResults.set(deviceId, []);
    }
    this.diagnosticResults.get(deviceId)!.push(diagnostic);

    return diagnostic;
  }

  /**
   * 执行健康检�?   */
  performHealthCheck(deviceId: string, threshold: any): any {
    const diagnosticId = this.generateDeviceId();
    const healthScore = Math.random() * 100;
    const issues = healthScore < (threshold.minHealth || 70) ? ['健康状态不佳'] : [];

    return {
      id: diagnosticId,
      deviceId,
      healthScore,
      issues,
      recommendations: issues.length > 0 ? ['进行维护检查'] : [],
      timestamp: new Date()
    };
  }

  /**
   * 性能测试
   */
  performanceTest(deviceId: string, _parameters: any): any {
    const diagnosticId = this.generateDeviceId();
    const performanceScore = Math.random() * 100;
    const issues = performanceScore < 80 ? ['性能不达标'] : [];

    return {
      id: diagnosticId,
      deviceId,
      performanceScore,
      issues,
      recommendations: issues.length > 0 ? ['优化设备配置'] : [],
      timestamp: new Date()
    };
  }

  /**
   * 执行校准
   */
  performCalibration(calibrationData: any): any {
    const calibrationId = this.generateDeviceId();
    const accuracy = Math.random() * 100;
    const deviation = {
      x: (Math.random() - 0.5) * 2,
      y: (Math.random() - 0.5) * 2,
      z: (Math.random() - 0.5) * 2
    };

    return {
      id: calibrationId,
      ...calibrationData,
      accuracy,
      deviation,
      certificate: {
        id: this.generateDeviceId(),
        issuedAt: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      },
      calibratedAt: new Date()
    };
  }

  /**
   * 验证校准
   */
  verifyCalibration(deviceId: string, _referenceValues: any, tolerance: any): any {
    const verificationId = this.generateDeviceId();
    const accuracy = Math.random() * 100;
    const passed = accuracy >= (tolerance.minAccuracy || 95);

    return {
      id: verificationId,
      deviceId,
      accuracy,
      passed,
      deviation: {
        x: (Math.random() - 0.5) * 1,
        y: (Math.random() - 0.5) * 1,
        z: (Math.random() - 0.5) * 1
      },
      verifiedAt: new Date()
    };
  }

  /**
   * 调整校准
   */
  adjustCalibration(deviceId: string, _referenceValues: any): any {
    const adjustmentId = this.generateDeviceId();
    return {
      id: adjustmentId,
      deviceId,
      accuracy: 98 + Math.random() * 2,
      deviation: {
        x: (Math.random() - 0.5) * 0.5,
        y: (Math.random() - 0.5) * 0.5,
        z: (Math.random() - 0.5) * 0.5
      },
      adjustedAt: new Date()
    };
  }

  /**
   * 获取配置
   */
  getConfiguration(deviceId: string, configKey: string, configGroup: string): any {
    const configId = this.generateDeviceId();
    return {
      id: configId,
      deviceId,
      configKey,
      configGroup,
      value: `config_value_${Math.random()}`,
      retrievedAt: new Date()
    };
  }

  /**
   * 设置配置
   */
  setConfiguration(deviceId: string, configKey: string, configValue: any, backup: boolean): any {
    const configId = this.generateDeviceId();
    const configuration = {
      id: configId,
      deviceId,
      configKey,
      value: configValue,
      backup: backup ? {
        id: this.generateDeviceId(),
        previousValue: `previous_${Math.random()}`,
        backedUpAt: new Date()
      } : null,
      updatedAt: new Date()
    };

    return configuration;
  }

  /**
   * 备份配置
   */
  backupConfiguration(deviceId: string): any {
    const backupId = this.generateDeviceId();
    return {
      id: backupId,
      deviceId,
      configurations: {
        param1: 'value1',
        param2: 'value2',
        param3: 'value3'
      },
      backedUpAt: new Date()
    };
  }

  /**
   * 恢复配置
   */
  restoreConfiguration(deviceId: string, configGroup: string): any {
    const configId = this.generateDeviceId();
    return {
      id: configId,
      deviceId,
      configGroup,
      restoredConfigurations: {
        param1: 'restored_value1',
        param2: 'restored_value2'
      },
      restoredAt: new Date()
    };
  }

  /**
   * 重置配置
   */
  resetConfiguration(deviceId: string): any {
    const configId = this.generateDeviceId();
    return {
      id: configId,
      deviceId,
      resetToDefaults: true,
      resetAt: new Date()
    };
  }

  /**
   * 监控性能
   */
  monitorPerformance(deviceId: string, _timeRange: string, _metrics: string[]): any {
    const performanceId = this.generateDeviceId();
    const efficiency = Math.random() * 100;
    const utilization = Math.random() * 100;
    const throughput = Math.random() * 1000;

    return {
      id: performanceId,
      deviceId,
      timeRange: _timeRange,
      efficiency,
      utilization,
      throughput,
      trends: Array.from({ length: 24 }, () => Math.random() * 100),
      monitoredAt: new Date()
    };
  }

  /**
   * 分析性能
   */
  analyzePerformance(deviceId: string, timeRange: string, benchmark: any): any {
    const analysisId = this.generateDeviceId();
    const efficiency = Math.random() * 100;

    return {
      id: analysisId,
      deviceId,
      timeRange,
      efficiency,
      utilization: Math.random() * 100,
      throughput: Math.random() * 1000,
      trends: Array.from({ length: 24 }, () => Math.random() * 100),
      comparison: {
        benchmarkEfficiency: benchmark.efficiency || 85,
        improvement: efficiency - (benchmark.efficiency || 85)
      },
      analyzedAt: new Date()
    };
  }

  /**
   * 性能基准对比
   */
  benchmarkPerformance(deviceId: string, benchmark: any): any {
    const comparisonId = this.generateDeviceId();
    const currentEfficiency = Math.random() * 100;

    return {
      id: comparisonId,
      deviceId,
      currentEfficiency,
      utilization: Math.random() * 100,
      throughput: Math.random() * 1000,
      benchmark,
      comparison: {
        efficiencyGap: currentEfficiency - (benchmark.efficiency || 85),
        performanceBetter: currentEfficiency > (benchmark.efficiency || 85)
      },
      comparedAt: new Date()
    };
  }

  /**
   * 创建告警
   */
  createAlert(alertData: any): any {
    const alertId = this.generateDeviceId();
    return {
      id: alertId,
      ...alertData,
      status: 'active',
      createdAt: new Date()
    };
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(deviceId: string): any[] {
    return [
      {
        id: this.generateDeviceId(),
        deviceId,
        alertType: 'temperature',
        severity: 'warning',
        message: '温度过高',
        createdAt: new Date()
      },
      {
        id: this.generateDeviceId(),
        deviceId,
        alertType: 'vibration',
        severity: 'critical',
        message: '振动异常',
        createdAt: new Date()
      }
    ];
  }

  /**
   * 解决告警
   */
  resolveAlert(_deviceId: string, _alertType: string): boolean {
    return Math.random() > 0.2; // 80%成功率
  }

  /**
   * 升级告警
   */
  escalateAlert(deviceId: string, alertType: string): any {
    return {
      id: this.generateDeviceId(),
      deviceId,
      alertType,
      escalatedTo: 'supervisor',
      escalatedAt: new Date()
    };
  }

  /**
   * 检查阈值
   */
  checkThresholds(deviceId: string, threshold: any): any[] {
    const alerts = [];

    // 模拟阈值检查
    if (Math.random() > 0.7) {
      alerts.push({
        id: this.generateDeviceId(),
        deviceId,
        alertType: 'threshold_exceeded',
        parameter: 'temperature',
        currentValue: 85,
        thresholdValue: 80,
        severity: 'warning'
      });
    }

    return alerts;
  }

  /**
   * 跟踪生命周期
   */
  trackLifecycle(deviceId: string): any {
    const lifecycleId = this.generateDeviceId();
    const installDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    const age = Math.floor((Date.now() - installDate.getTime()) / (24 * 60 * 60 * 1000));
    const expectedLife = 3650; // 10年
    const remainingLife = expectedLife - age;

    return {
      id: lifecycleId,
      deviceId,
      currentStage: age < 365 ? 'new' : age < 1825 ? 'mature' : 'aging',
      installDate,
      age,
      remainingLife: Math.max(0, remainingLife),
      milestones: [
        { name: 'installation', date: installDate },
        { name: 'commissioning', date: new Date(installDate.getTime() + 7 * 24 * 60 * 60 * 1000) }
      ]
    };
  }

  /**
   * 更新生命周期阶段
   */
  updateLifecycleStage(deviceId: string, stage: string, metadata: any): any {
    const lifecycleId = this.generateDeviceId();
    const lifecycle = this.trackLifecycle(deviceId);

    return {
      ...lifecycle,
      id: lifecycleId,
      currentStage: stage,
      updatedAt: new Date(),
      metadata
    };
  }

  /**
   * 添加生命周期里程碑
   */
  addLifecycleMilestone(deviceId: string, milestone: string, metadata: any): any {
    const lifecycle = this.trackLifecycle(deviceId);

    lifecycle.milestones.push({
      name: milestone,
      date: new Date(),
      metadata
    });

    return lifecycle;
  }

  /**
   * 计算设备年龄
   */
  calculateDeviceAge(deviceId: string): any {
    const lifecycle = this.trackLifecycle(deviceId);
    return lifecycle;
  }

  /**
   * 预测剩余寿命
   */
  predictRemainingLife(deviceId: string): any {
    const lifecycle = this.trackLifecycle(deviceId);
    const usageIntensity = Math.random(); // 使用强度
    const maintenanceQuality = Math.random(); // 维护质量

    // 基于使用强度和维护质量调整剩余寿命
    const adjustmentFactor = (1 - usageIntensity * 0.3) * (0.7 + maintenanceQuality * 0.3);
    const adjustedRemainingLife = Math.floor(lifecycle.remainingLife * adjustmentFactor);

    return {
      ...lifecycle,
      remainingLife: Math.max(0, adjustedRemainingLife),
      prediction: {
        usageIntensity,
        maintenanceQuality,
        adjustmentFactor,
        confidence: Math.random() * 0.3 + 0.7 // 70-100%置信度
      }
    };
  }

  /**
   * 生成设备ID
   */
  private generateDeviceId(): string {
    return 'dev_' + Math.random().toString(36).substr(2, 9);
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('DeviceManagementManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }
}

// 全局设备管理器实�?const deviceManagementManager = new DeviceManagementManager();

/**
 * 设备连接节点
 */
export class DeviceConnectionNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceConnection';
  public static readonly NAME = '设备连接';
  public static readonly DESCRIPTION = '管理设备的连接和断开';

  constructor(nodeType: string = DeviceConnectionNode.TYPE, name: string = DeviceConnectionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('register', 'trigger', '注册设备');
    this.addInput('connect', 'trigger', '连接设备');
    this.addInput('disconnect', 'trigger', '断开连接');
    this.addInput('getStatus', 'trigger', '获取状态');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('deviceName', 'string', '设备名称');
    this.addInput('deviceType', 'string', '设备类型');
    this.addInput('model', 'string', '设备型号');
    this.addInput('manufacturer', 'string', '制造商');
    this.addInput('serialNumber', 'string', '序列号');
    this.addInput('location', 'string', '位置');
    this.addInput('protocol', 'string', '通信协议');
    this.addInput('address', 'string', '设备地址');
    this.addInput('port', 'number', '端口号');

    // 输出端口
    this.addOutput('device', 'object', '设备信息');
    this.addOutput('deviceId', 'string', '设备ID');
    this.addOutput('status', 'string', '设备状态');
    this.addOutput('lastUpdate', 'string', '最后更新时间');
    this.addOutput('onRegistered', 'trigger', '设备注册完成');
    this.addOutput('onConnected', 'trigger', '设备连接成功');
    this.addOutput('onDisconnected', 'trigger', '设备断开连接');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const registerTrigger = inputs?.register;
      const connectTrigger = inputs?.connect;
      const disconnectTrigger = inputs?.disconnect;
      const getStatusTrigger = inputs?.getStatus;

      if (registerTrigger) {
        return this.registerDevice(inputs);
      } else if (connectTrigger) {
        // 对于异步操作，返回Promise
        return this.connectDevice(inputs);
      } else if (disconnectTrigger) {
        return this.disconnectDevice(inputs);
      } else if (getStatusTrigger) {
        return this.getDeviceStatus(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('DeviceConnectionNode', '设备连接操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private registerDevice(inputs: any): any {
    const deviceId = inputs?.deviceId as string || deviceManagementManager.generateDeviceId();
    const deviceName = inputs?.deviceName as string || 'Unknown Device';
    const deviceType = inputs?.deviceType as DeviceType || DeviceType.MACHINE;
    const model = inputs?.model as string || '';
    const manufacturer = inputs?.manufacturer as string || '';
    const serialNumber = inputs?.serialNumber as string || '';
    const location = inputs?.location as string || '';
    const protocol = inputs?.protocol as ConnectionProtocol || ConnectionProtocol.MODBUS_TCP;
    const address = inputs?.address as string || '';
    const port = inputs?.port as number || 502;

    const deviceInfo: DeviceInfo = {
      id: deviceId,
      name: deviceName,
      type: deviceType,
      model,
      manufacturer,
      serialNumber,
      location,
      status: DeviceStatus.OFFLINE,
      protocol,
      address,
      port,
      lastUpdate: new Date(),
      properties: {},
      tags: []
    };

    const success = deviceManagementManager.registerDevice(deviceInfo);
    if (!success) {
      throw new Error('设备注册失败');
    }

    Debug.log('DeviceConnectionNode', `设备注册成功: ${deviceName} (${deviceId})`);

    return {
      device: deviceInfo,
      deviceId,
      status: deviceInfo.status,
      lastUpdate: deviceInfo.lastUpdate.toISOString(),
      onRegistered: true,
      onConnected: false,
      onDisconnected: false,
      onError: false
    };
  }

  private async connectDevice(inputs: any): Promise<any> {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    await deviceManagementManager.connectDevice(deviceId);
    const device = deviceManagementManager.getDevice(deviceId);

    Debug.log('DeviceConnectionNode', `设备连接成功: ${deviceId}`);

    return {
      device,
      deviceId,
      status: device?.status || DeviceStatus.ERROR,
      lastUpdate: device?.lastUpdate.toISOString() || new Date().toISOString(),
      onRegistered: false,
      onConnected: true,
      onDisconnected: false,
      onError: false
    };
  }

  private disconnectDevice(inputs: any): any {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const success = deviceManagementManager.disconnectDevice(deviceId);
    if (!success) {
      throw new Error('设备断开连接失败');
    }

    const device = deviceManagementManager.getDevice(deviceId);

    Debug.log('DeviceConnectionNode', `设备断开连接: ${deviceId}`);

    return {
      device,
      deviceId,
      status: device?.status || DeviceStatus.OFFLINE,
      lastUpdate: device?.lastUpdate.toISOString() || new Date().toISOString(),
      onRegistered: false,
      onConnected: false,
      onDisconnected: true,
      onError: false
    };
  }

  private getDeviceStatus(inputs: any): any {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    return {
      device,
      deviceId,
      status: device.status,
      lastUpdate: device.lastUpdate.toISOString(),
      onRegistered: false,
      onConnected: false,
      onDisconnected: false,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      device: null,
      deviceId: '',
      status: '',
      lastUpdate: '',
      onRegistered: false,
      onConnected: false,
      onDisconnected: false,
      onError: false
    };
  }
}

/**
 * 设备监控节点
 */
export class DeviceMonitoringNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceMonitoring';
  public static readonly NAME = '设备监控';
  public static readonly DESCRIPTION = '监控设备运行状态和参数';

  constructor(nodeType: string = DeviceMonitoringNode.TYPE, name: string = DeviceMonitoringNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('startMonitoring', 'trigger', '开始监控');
    this.addInput('stopMonitoring', 'trigger', '停止监控');
    this.addInput('getStatus', 'trigger', '获取状态');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('interval', 'number', '监控间隔(秒)');
    this.addInput('parameters', 'array', '监控参数');

    // 输出端口
    this.addOutput('monitoringData', 'object', '监控数据');
    this.addOutput('deviceStatus', 'string', '设备状态');
    this.addOutput('parameters', 'array', '参数值');
    this.addOutput('uptime', 'number', '运行时间');
    this.addOutput('errorCount', 'number', '错误计数');
    this.addOutput('onMonitoringStarted', 'trigger', '监控开�?);
    this.addOutput('onMonitoringStopped', 'trigger', '监控停止');
    this.addOutput('onDataUpdated', 'trigger', '数据更新');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const startTrigger = inputs?.startMonitoring;
      const stopTrigger = inputs?.stopMonitoring;
      const getStatusTrigger = inputs?.getStatus;

      if (startTrigger) {
        return this.startMonitoring(inputs);
      } else if (stopTrigger) {
        return this.stopMonitoring(inputs);
      } else if (getStatusTrigger) {
        return this.getMonitoringStatus(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('DeviceMonitoringNode', '设备监控操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private startMonitoring(inputs: any): any {
    const deviceId = inputs?.deviceId as string;
    const interval = inputs?.interval as number || 60;
    const parameters = inputs?.parameters as string[] || ['status', 'temperature', 'pressure'];

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    // 模拟开始监控
    const monitoringData = this.generateMonitoringData(deviceId, parameters);

    Debug.log('DeviceMonitoringNode', `开始监控设�? ${deviceId}, 间隔: ${interval}秒`);

    return {
      monitoringData,
      deviceStatus: device.status,
      parameters: monitoringData.parameters,
      uptime: monitoringData.uptime,
      errorCount: monitoringData.errorCount,
      onMonitoringStarted: true,
      onMonitoringStopped: false,
      onDataUpdated: false,
      onError: false
    };
  }

  private stopMonitoring(inputs: any): any {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    Debug.log('DeviceMonitoringNode', `停止监控设备: ${deviceId}`);

    return {
      monitoringData: null,
      deviceStatus: '',
      parameters: [],
      uptime: 0,
      errorCount: 0,
      onMonitoringStarted: false,
      onMonitoringStopped: true,
      onDataUpdated: false,
      onError: false
    };
  }

  private getMonitoringStatus(inputs: any): any {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    const monitoringData = this.generateMonitoringData(deviceId, ['status', 'temperature', 'pressure']);

    return {
      monitoringData,
      deviceStatus: device.status,
      parameters: monitoringData.parameters,
      uptime: monitoringData.uptime,
      errorCount: monitoringData.errorCount,
      onMonitoringStarted: false,
      onMonitoringStopped: false,
      onDataUpdated: true,
      onError: false
    };
  }

  private generateMonitoringData(deviceId: string, parameters: string[]): DeviceMonitorData {
    return {
      deviceId,
      timestamp: new Date(),
      parameters: parameters.map(param => ({
        name: param,
        value: this.generateParameterValue(param),
        unit: this.getParameterUnit(param),
        quality: 0.95 + Math.random() * 0.05,
        alarm: Math.random() < 0.05,
        warning: Math.random() < 0.1
      })),
      status: DeviceStatus.RUNNING,
      uptime: Math.floor(Math.random() * 10000),
      errorCount: Math.floor(Math.random() * 5),
      warningCount: Math.floor(Math.random() * 10)
    };
  }

  private generateParameterValue(parameter: string): any {
    switch (parameter.toLowerCase()) {
      case 'temperature':
        return 20 + Math.random() * 60;
      case 'pressure':
        return 1 + Math.random() * 10;
      case 'voltage':
        return 220 + Math.random() * 20;
      case 'current':
        return Math.random() * 10;
      case 'speed':
        return Math.random() * 1000;
      case 'status':
        return Math.random() > 0.1 ? 'running' : 'stopped';
      default:
        return Math.random() * 100;
    }
  }

  private getParameterUnit(parameter: string): string {
    switch (parameter.toLowerCase()) {
      case 'temperature':
        return '°C';
      case 'pressure':
        return 'bar';
      case 'voltage':
        return 'V';
      case 'current':
        return 'A';
      case 'speed':
        return 'rpm';
      case 'status':
        return '';
      default:
        return '';
    }
  }

  private getDefaultOutputs(): any {
    return {
      monitoringData: null,
      deviceStatus: '',
      parameters: [],
      uptime: 0,
      errorCount: 0,
      onMonitoringStarted: false,
      onMonitoringStopped: false,
      onDataUpdated: false,
      onError: false
    };
  }
}

/**
 * 设备控制节点
 */
export class DeviceControlNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceControl';
  public static readonly NAME = '设备控制';
  public static readonly DESCRIPTION = '控制设备的启动、停止和参数设置';

  constructor(nodeType: string = DeviceControlNode.TYPE, name: string = DeviceControlNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('start', 'trigger', '启动设备');
    this.addInput('stop', 'trigger', '停止设备');
    this.addInput('reset', 'trigger', '重置设备');
    this.addInput('setParameter', 'trigger', '设置参数');
    this.addInput('sendCommand', 'trigger', '发送命令');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('parameter', 'string', '参数名称');
    this.addInput('value', 'any', '参数值');
    this.addInput('command', 'string', '控制命令');
    this.addInput('parameters', 'object', '命令参数');

    // 输出端口
    this.addOutput('result', 'boolean', '操作结果');
    this.addOutput('deviceStatus', 'string', '设备状态');
    this.addOutput('response', 'any', '响应数据');
    this.addOutput('commandId', 'string', '命令ID');
    this.addOutput('onStarted', 'trigger', '设备启动');
    this.addOutput('onStopped', 'trigger', '设备停止');
    this.addOutput('onReset', 'trigger', '设备重置');
    this.addOutput('onParameterSet', 'trigger', '参数设置完成');
    this.addOutput('onCommandSent', 'trigger', '命令发送完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;
      const resetTrigger = inputs?.reset;
      const setParameterTrigger = inputs?.setParameter;
      const sendCommandTrigger = inputs?.sendCommand;

      if (startTrigger) {
        return this.startDevice(inputs);
      } else if (stopTrigger) {
        return this.stopDevice(inputs);
      } else if (resetTrigger) {
        return this.resetDevice(inputs);
      } else if (setParameterTrigger) {
        return this.setParameter(inputs);
      } else if (sendCommandTrigger) {
        return this.sendCommand(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('DeviceControlNode', '设备控制操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async startDevice(inputs: any): Promise<any> {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    // 模拟设备启动
    await this.simulateOperation(500);

    const success = deviceManagementManager.updateDeviceStatus(deviceId, DeviceStatus.RUNNING);
    if (!success) {
      throw new Error('设备启动失败');
    }

    Debug.log('DeviceControlNode', `设备启动成功: ${deviceId}`);

    return {
      result: true,
      deviceStatus: DeviceStatus.RUNNING,
      response: { message: '设备启动成功', timestamp: new Date().toISOString() },
      commandId: this.generateCommandId(),
      onStarted: true,
      onStopped: false,
      onReset: false,
      onParameterSet: false,
      onCommandSent: false,
      onError: false
    };
  }

  private async stopDevice(inputs: any): Promise<any> {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    // 模拟设备停止
    await this.simulateOperation(300);

    const success = deviceManagementManager.updateDeviceStatus(deviceId, DeviceStatus.STOPPED);
    if (!success) {
      throw new Error('设备停止失败');
    }

    Debug.log('DeviceControlNode', `设备停止成功: ${deviceId}`);

    return {
      result: true,
      deviceStatus: DeviceStatus.STOPPED,
      response: { message: '设备停止成功', timestamp: new Date().toISOString() },
      commandId: this.generateCommandId(),
      onStarted: false,
      onStopped: true,
      onReset: false,
      onParameterSet: false,
      onCommandSent: false,
      onError: false
    };
  }

  private async resetDevice(inputs: any): Promise<any> {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    // 模拟设备重置
    await this.simulateOperation(1000);

    const success = deviceManagementManager.updateDeviceStatus(deviceId, DeviceStatus.IDLE);
    if (!success) {
      throw new Error('设备重置失败');
    }

    Debug.log('DeviceControlNode', `设备重置成功: ${deviceId}`);

    return {
      result: true,
      deviceStatus: DeviceStatus.IDLE,
      response: { message: '设备重置成功', timestamp: new Date().toISOString() },
      commandId: this.generateCommandId(),
      onStarted: false,
      onStopped: false,
      onReset: true,
      onParameterSet: false,
      onCommandSent: false,
      onError: false
    };
  }

  private async setParameter(inputs: any): Promise<any> {
    const deviceId = inputs?.deviceId as string;
    const parameter = inputs?.parameter as string;
    const value = inputs?.value;

    if (!deviceId || !parameter) {
      throw new Error('未提供设备ID或参数名称');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    // 模拟参数设置
    await this.simulateOperation(200);

    // 更新设备属性
    device.properties[parameter] = value;

    Debug.log('DeviceControlNode', `参数设置成功: ${deviceId}.${parameter} = ${value}`);

    return {
      result: true,
      deviceStatus: device.status,
      response: {
        message: '参数设置成功',
        parameter,
        value,
        timestamp: new Date().toISOString()
      },
      commandId: this.generateCommandId(),
      onStarted: false,
      onStopped: false,
      onReset: false,
      onParameterSet: true,
      onCommandSent: false,
      onError: false
    };
  }

  private async sendCommand(inputs: any): Promise<any> {
    const deviceId = inputs?.deviceId as string;
    const command = inputs?.command as string;
    const parameters = inputs?.parameters as Record<string, any> || {};

    if (!deviceId || !command) {
      throw new Error('未提供设备ID或命令');
    }

    const device = deviceManagementManager.getDevice(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    // 模拟命令发送
    await this.simulateOperation(300);

    const commandId = this.generateCommandId();

    Debug.log('DeviceControlNode', `命令发送成�? ${deviceId} - ${command}`);

    return {
      result: true,
      deviceStatus: device.status,
      response: {
        message: '命令发送成功',
        command,
        parameters,
        timestamp: new Date().toISOString()
      },
      commandId,
      onStarted: false,
      onStopped: false,
      onReset: false,
      onParameterSet: false,
      onCommandSent: true,
      onError: false
    };
  }

  private async simulateOperation(delay: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  private generateCommandId(): string {
    return 'CMD_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 5);
  }

  private getDefaultOutputs(): any {
    return {
      result: false,
      deviceStatus: '',
      response: null,
      commandId: '',
      onStarted: false,
      onStopped: false,
      onReset: false,
      onParameterSet: false,
      onCommandSent: false,
      onError: false
    };
  }
}

/**
 * 设备维护节点
 */
export class DeviceMaintenanceNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceMaintenance';
  public static readonly NAME = '设备维护';
  public static readonly DESCRIPTION = '管理设备的维护计划和执行';

  constructor(nodeType: string = DeviceMaintenanceNode.TYPE, name: string = DeviceMaintenanceNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('action', 'string', '维护动作', 'schedule');
    this.addInput('deviceId', 'string', '设备ID', '');
    this.addInput('maintenanceType', 'string', '维护类型', 'preventive');
    this.addInput('scheduledDate', 'string', '计划日期', '');
    this.addInput('technician', 'string', '技术员', '');
    this.addInput('description', 'string', '维护描述', '');
    this.addInput('priority', 'string', '优先级', 'medium');
  }

  private setupOutputs(): void {
    this.addOutput('maintenance', 'object', '维护记录');
    this.addOutput('maintenanceId', 'string', '维护ID');
    this.addOutput('status', 'string', '维护状态');
    this.addOutput('nextMaintenance', 'string', '下次维护时间');
    this.addOutput('onScheduled', 'boolean', '维护已调度');
    this.addOutput('onStarted', 'boolean', '维护已开始');
    this.addOutput('onCompleted', 'boolean', '维护已完成');
    this.addOutput('onCancelled', 'boolean', '维护已取消');
    this.addOutput('onError', 'boolean', '维护错误');
  }

  public execute(inputs: any): any {
    const action = inputs?.action as string || 'schedule';
    const deviceId = inputs?.deviceId as string;
    const maintenanceType = inputs?.maintenanceType as string || 'preventive';
    const scheduledDate = inputs?.scheduledDate as string;
    const technician = inputs?.technician as string;
    const description = inputs?.description as string || '';
    const priority = inputs?.priority as string || 'medium';

    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      let result: any;

      switch (action) {
        case 'schedule':
          result = this.scheduleMaintenance(deviceId, maintenanceType, scheduledDate, technician, description, priority);
          break;
        case 'start':
          result = this.startMaintenance(deviceId, technician);
          break;
        case 'complete':
          result = this.completeMaintenance(deviceId, description);
          break;
        case 'cancel':
          result = this.cancelMaintenance(deviceId);
          break;
        default:
          throw new Error(`不支持的维护动作: ${action}`);
      }

      return result;
    } catch (error) {
      Debug.error('DeviceMaintenanceNode', '设备维护操作失败', error);
      return {
        maintenance: null,
        maintenanceId: '',
        status: 'error',
        nextMaintenance: '',
        onScheduled: false,
        onStarted: false,
        onCompleted: false,
        onCancelled: false,
        onError: true
      };
    }
  }

  private async scheduleMaintenance(deviceId: string, maintenanceType: string, scheduledDate: string, technician: string, description: string, priority: string): Promise<any> {
    const maintenance = deviceManagementManager.scheduleMaintenance({
      deviceId,
      maintenanceType,
      scheduledDate: new Date(scheduledDate),
      technician,
      description,
      priority
    });

    const nextMaintenance = deviceManagementManager.getNextMaintenanceDate(deviceId);

    Debug.log('DeviceMaintenanceNode', `设备维护调度: ${deviceId} - ${scheduledDate}`);

    return {
      maintenance,
      maintenanceId: maintenance.id,
      status: 'scheduled',
      nextMaintenance: nextMaintenance.toISOString(),
      onScheduled: true,
      onStarted: false,
      onCompleted: false,
      onCancelled: false,
      onError: false
    };
  }

  private async startMaintenance(deviceId: string, technician: string): Promise<any> {
    const maintenance = deviceManagementManager.startMaintenance(deviceId, technician);

    Debug.log('DeviceMaintenanceNode', `设备维护开始: ${deviceId}`);

    return {
      maintenance,
      maintenanceId: maintenance.id,
      status: 'in_progress',
      nextMaintenance: '',
      onScheduled: false,
      onStarted: true,
      onCompleted: false,
      onCancelled: false,
      onError: false
    };
  }

  private async completeMaintenance(deviceId: string, description: string): Promise<any> {
    const maintenance = deviceManagementManager.completeMaintenance(deviceId, description);
    const nextMaintenance = deviceManagementManager.getNextMaintenanceDate(deviceId);

    Debug.log('DeviceMaintenanceNode', `设备维护完成: ${deviceId}`);

    return {
      maintenance,
      maintenanceId: maintenance.id,
      status: 'completed',
      nextMaintenance: nextMaintenance.toISOString(),
      onScheduled: false,
      onStarted: false,
      onCompleted: true,
      onCancelled: false,
      onError: false
    };
  }

  private async cancelMaintenance(deviceId: string): Promise<any> {
    const success = deviceManagementManager.cancelMaintenance(deviceId);

    Debug.log('DeviceMaintenanceNode', `设备维护取消: ${deviceId}`);

    return {
      maintenance: null,
      maintenanceId: '',
      status: 'cancelled',
      nextMaintenance: '',
      onScheduled: false,
      onStarted: false,
      onCompleted: false,
      onCancelled: success,
      onError: !success
    };
  }
}

/**
 * 设备诊断节点
 */
export class DeviceDiagnosticsNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceDiagnostics';
  public static readonly NAME = '设备诊断';
  public static readonly DESCRIPTION = '诊断设备故障和健康状态';

  constructor(nodeType: string = DeviceDiagnosticsNode.TYPE, name: string = DeviceDiagnosticsNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('action', 'string', '诊断动作', 'diagnose');
    this.addInput('deviceId', 'string', '设备ID', '');
    this.addInput('diagnosticType', 'string', '诊断类型', 'health');
    this.addInput('parameters', 'object', '诊断参数', {});
    this.addInput('threshold', 'object', '阈值设置', {});
  }

  private setupOutputs(): void {
    this.addOutput('diagnostic', 'object', '诊断结果');
    this.addOutput('diagnosticId', 'string', '诊断ID');
    this.addOutput('healthScore', 'number', '健康评分');
    this.addOutput('issues', 'array', '发现问题');
    this.addOutput('recommendations', 'array', '建议措施');
    this.addOutput('onHealthy', 'boolean', '设备健康');
    this.addOutput('onWarning', 'boolean', '设备警告');
    this.addOutput('onCritical', 'boolean', '设备严重');
    this.addOutput('onError', 'boolean', '诊断错误');
  }

  public execute(inputs: any): any {
    const action = inputs?.action as string || 'diagnose';
    const deviceId = inputs?.deviceId as string;
    const diagnosticType = inputs?.diagnosticType as string || 'health';
    const parameters = inputs?.parameters as any || {};
    const threshold = inputs?.threshold as any || {};

    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      let result: any;

      switch (action) {
        case 'diagnose':
          result = this.runDiagnostic(deviceId, diagnosticType, parameters, threshold);
          break;
        case 'health_check':
          result = this.performHealthCheck(deviceId, threshold);
          break;
        case 'performance_test':
          result = this.performanceTest(deviceId, parameters);
          break;
        default:
          throw new Error(`不支持的诊断动作: ${action}`);
      }

      return result;
    } catch (error) {
      Debug.error('DeviceDiagnosticsNode', '设备诊断失败', error);
      return {
        diagnostic: null,
        diagnosticId: '',
        healthScore: 0,
        issues: [],
        recommendations: [],
        onHealthy: false,
        onWarning: false,
        onCritical: false,
        onError: true
      };
    }
  }

  private async runDiagnostic(deviceId: string, diagnosticType: string, parameters: any, threshold: any): Promise<any> {
    const diagnostic = deviceManagementManager.runDiagnostic(deviceId, diagnosticType, parameters, threshold);
    const healthScore = diagnostic.healthScore;
    const issues = diagnostic.issues;
    const recommendations = diagnostic.recommendations;

    Debug.log('DeviceDiagnosticsNode', `设备诊断完成: ${deviceId} - 健康评分: ${healthScore}`);

    return {
      diagnostic,
      diagnosticId: diagnostic.id,
      healthScore,
      issues,
      recommendations,
      onHealthy: healthScore >= 80,
      onWarning: healthScore >= 60 && healthScore < 80,
      onCritical: healthScore < 60,
      onError: false
    };
  }

  private async performHealthCheck(deviceId: string, threshold: any): Promise<any> {
    const diagnostic = deviceManagementManager.performHealthCheck(deviceId, threshold);
    const healthScore = diagnostic.healthScore;

    Debug.log('DeviceDiagnosticsNode', `设备健康检查: ${deviceId} - ${healthScore}分`);

    return {
      diagnostic,
      diagnosticId: diagnostic.id,
      healthScore,
      issues: diagnostic.issues,
      recommendations: diagnostic.recommendations,
      onHealthy: healthScore >= 80,
      onWarning: healthScore >= 60 && healthScore < 80,
      onCritical: healthScore < 60,
      onError: false
    };
  }

  private async performanceTest(deviceId: string, parameters: any): Promise<any> {
    const diagnostic = deviceManagementManager.performanceTest(deviceId, parameters);
    const healthScore = diagnostic.performanceScore;

    Debug.log('DeviceDiagnosticsNode', `设备性能测试: ${deviceId} - ${healthScore}分`);

    return {
      diagnostic,
      diagnosticId: diagnostic.id,
      healthScore,
      issues: diagnostic.issues,
      recommendations: diagnostic.recommendations,
      onHealthy: healthScore >= 80,
      onWarning: healthScore >= 60 && healthScore < 80,
      onCritical: healthScore < 60,
      onError: false
    };
  }
}

/**
 * 设备校准节点
 */
export class DeviceCalibrationNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceCalibration';
  public static readonly NAME = '设备校准';
  public static readonly DESCRIPTION = '管理设备的校准和标定';

  constructor(nodeType: string = DeviceCalibrationNode.TYPE, name: string = DeviceCalibrationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('action', 'string', '校准动作', 'calibrate');
    this.addInput('deviceId', 'string', '设备ID', '');
    this.addInput('calibrationType', 'string', '校准类型', 'standard');
    this.addInput('referenceValues', 'object', '参考值', {});
    this.addInput('tolerance', 'object', '容差设置', {});
    this.addInput('operator', 'string', '操作员', '');
  }

  private setupOutputs(): void {
    this.addOutput('calibration', 'object', '校准结果');
    this.addOutput('calibrationId', 'string', '校准ID');
    this.addOutput('accuracy', 'number', '精度');
    this.addOutput('deviation', 'object', '偏差数据');
    this.addOutput('certificate', 'object', '校准证书');
    this.addOutput('onCalibrated', 'boolean', '校准完成');
    this.addOutput('onPassed', 'boolean', '校准通过');
    this.addOutput('onFailed', 'boolean', '校准失败');
    this.addOutput('onError', 'boolean', '校准错误');
  }

  public execute(inputs: any): any {
    const action = inputs?.action as string || 'calibrate';
    const deviceId = inputs?.deviceId as string;
    const calibrationType = inputs?.calibrationType as string || 'standard';
    const referenceValues = inputs?.referenceValues as any || {};
    const tolerance = inputs?.tolerance as any || {};
    const operator = inputs?.operator as string || 'system';

    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      let result: any;

      switch (action) {
        case 'calibrate':
          result = this.performCalibration(deviceId, calibrationType, referenceValues, tolerance, operator);
          break;
        case 'verify':
          result = this.verifyCalibration(deviceId, referenceValues, tolerance);
          break;
        case 'adjust':
          result = this.adjustCalibration(deviceId, referenceValues);
          break;
        default:
          throw new Error(`不支持的校准动作: ${action}`);
      }

      return result;
    } catch (error) {
      Debug.error('DeviceCalibrationNode', '设备校准失败', error);
      return {
        calibration: null,
        calibrationId: '',
        accuracy: 0,
        deviation: null,
        certificate: null,
        onCalibrated: false,
        onPassed: false,
        onFailed: false,
        onError: true
      };
    }
  }

  private async performCalibration(deviceId: string, calibrationType: string, referenceValues: any, tolerance: any, operator: string): Promise<any> {
    const calibration = deviceManagementManager.performCalibration({
      deviceId,
      calibrationType,
      referenceValues,
      tolerance,
      operator
    });

    const accuracy = calibration.accuracy;
    const passed = accuracy >= (tolerance.minAccuracy || 95);

    Debug.log('DeviceCalibrationNode', `设备校准完成: ${deviceId} - 精度: ${accuracy}%`);

    return {
      calibration,
      calibrationId: calibration.id,
      accuracy,
      deviation: calibration.deviation,
      certificate: calibration.certificate,
      onCalibrated: true,
      onPassed: passed,
      onFailed: !passed,
      onError: false
    };
  }

  private async verifyCalibration(deviceId: string, referenceValues: any, tolerance: any): Promise<any> {
    const verification = deviceManagementManager.verifyCalibration(deviceId, referenceValues, tolerance);
    const passed = verification.passed;

    Debug.log('DeviceCalibrationNode', `设备校准验证: ${deviceId} - ${passed ? '通过' : '失败'}`);

    return {
      calibration: verification,
      calibrationId: verification.id,
      accuracy: verification.accuracy,
      deviation: verification.deviation,
      certificate: null,
      onCalibrated: false,
      onPassed: passed,
      onFailed: !passed,
      onError: false
    };
  }

  private async adjustCalibration(deviceId: string, referenceValues: any): Promise<any> {
    const adjustment = deviceManagementManager.adjustCalibration(deviceId, referenceValues);

    Debug.log('DeviceCalibrationNode', `设备校准调整: ${deviceId}`);

    return {
      calibration: adjustment,
      calibrationId: adjustment.id,
      accuracy: adjustment.accuracy,
      deviation: adjustment.deviation,
      certificate: null,
      onCalibrated: true,
      onPassed: true,
      onFailed: false,
      onError: false
    };
  }
}

/**
 * 设备配置节点
 */
export class DeviceConfigurationNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceConfiguration';
  public static readonly NAME = '设备配置';
  public static readonly DESCRIPTION = '管理设备的配置参数';

  constructor(nodeType: string = DeviceConfigurationNode.TYPE, name: string = DeviceConfigurationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('action', 'string', '配置动作', 'get');
    this.addInput('deviceId', 'string', '设备ID', '');
    this.addInput('configKey', 'string', '配置键', '');
    this.addInput('configValue', 'any', '配置值', null);
    this.addInput('configGroup', 'string', '配置组', '');
    this.addInput('backup', 'boolean', '备份配置', true);
  }

  private setupOutputs(): void {
    this.addOutput('configuration', 'object', '配置数据');
    this.addOutput('configId', 'string', '配置ID');
    this.addOutput('value', 'any', '配置值');
    this.addOutput('backup', 'object', '备份数据');
    this.addOutput('onConfigured', 'boolean', '配置完成');
    this.addOutput('onRestored', 'boolean', '配置恢复');
    this.addOutput('onBackedUp', 'boolean', '配置备份');
    this.addOutput('onError', 'boolean', '配置错误');
  }

  public execute(inputs: any): any {
    const action = inputs?.action as string || 'get';
    const deviceId = inputs?.deviceId as string;
    const configKey = inputs?.configKey as string;
    const configValue = inputs?.configValue;
    const configGroup = inputs?.configGroup as string;
    const backup = inputs?.backup as boolean ?? true;

    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      let result: any;

      switch (action) {
        case 'get':
          result = this.getConfiguration(deviceId, configKey, configGroup);
          break;
        case 'set':
          result = this.setConfiguration(deviceId, configKey, configValue, backup);
          break;
        case 'backup':
          result = this.backupConfiguration(deviceId);
          break;
        case 'restore':
          result = this.restoreConfiguration(deviceId, configGroup);
          break;
        case 'reset':
          result = this.resetConfiguration(deviceId);
          break;
        default:
          throw new Error(`不支持的配置动作: ${action}`);
      }

      return result;
    } catch (error) {
      Debug.error('DeviceConfigurationNode', '设备配置操作失败', error);
      return {
        configuration: null,
        configId: '',
        value: null,
        backup: null,
        onConfigured: false,
        onRestored: false,
        onBackedUp: false,
        onError: true
      };
    }
  }

  private async getConfiguration(deviceId: string, configKey: string, configGroup: string): Promise<any> {
    const configuration = deviceManagementManager.getConfiguration(deviceId, configKey, configGroup);

    Debug.log('DeviceConfigurationNode', `获取设备配置: ${deviceId} - ${configKey}`);

    return {
      configuration,
      configId: configuration.id,
      value: configuration.value,
      backup: null,
      onConfigured: false,
      onRestored: false,
      onBackedUp: false,
      onError: false
    };
  }

  private async setConfiguration(deviceId: string, configKey: string, configValue: any, backup: boolean): Promise<any> {
    const configuration = deviceManagementManager.setConfiguration(deviceId, configKey, configValue, backup);

    Debug.log('DeviceConfigurationNode', `设置设备配置: ${deviceId} - ${configKey} = ${configValue}`);

    return {
      configuration,
      configId: configuration.id,
      value: configValue,
      backup: backup ? configuration.backup : null,
      onConfigured: true,
      onRestored: false,
      onBackedUp: backup,
      onError: false
    };
  }

  private async backupConfiguration(deviceId: string): Promise<any> {
    const backup = deviceManagementManager.backupConfiguration(deviceId);

    Debug.log('DeviceConfigurationNode', `备份设备配置: ${deviceId}`);

    return {
      configuration: null,
      configId: '',
      value: null,
      backup,
      onConfigured: false,
      onRestored: false,
      onBackedUp: true,
      onError: false
    };
  }

  private async restoreConfiguration(deviceId: string, configGroup: string): Promise<any> {
    const configuration = deviceManagementManager.restoreConfiguration(deviceId, configGroup);

    Debug.log('DeviceConfigurationNode', `恢复设备配置: ${deviceId}`);

    return {
      configuration,
      configId: configuration.id,
      value: null,
      backup: null,
      onConfigured: false,
      onRestored: true,
      onBackedUp: false,
      onError: false
    };
  }

  private async resetConfiguration(deviceId: string): Promise<any> {
    const configuration = deviceManagementManager.resetConfiguration(deviceId);

    Debug.log('DeviceConfigurationNode', `重置设备配置: ${deviceId}`);

    return {
      configuration,
      configId: configuration.id,
      value: null,
      backup: null,
      onConfigured: true,
      onRestored: false,
      onBackedUp: false,
      onError: false
    };
  }
}

/**
 * 设备性能节点
 */
export class DevicePerformanceNode extends VisualScriptNode {
  public static readonly TYPE = 'DevicePerformance';
  public static readonly NAME = '设备性能';
  public static readonly DESCRIPTION = '监控和分析设备性能指标';

  constructor(nodeType: string = DevicePerformanceNode.TYPE, name: string = DevicePerformanceNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('action', 'string', '性能动作', 'monitor');
    this.addInput('deviceId', 'string', '设备ID', '');
    this.addInput('timeRange', 'string', '时间范围', 'hour');
    this.addInput('metrics', 'array', '性能指标', []);
    this.addInput('benchmark', 'object', '基准值', {});
  }

  private setupOutputs(): void {
    this.addOutput('performance', 'object', '性能数据');
    this.addOutput('performanceId', 'string', '性能ID');
    this.addOutput('efficiency', 'number', '效率');
    this.addOutput('utilization', 'number', '利用率');
    this.addOutput('throughput', 'number', '吞吐量');
    this.addOutput('trends', 'array', '性能趋势');
    this.addOutput('onOptimal', 'boolean', '性能最佳');
    this.addOutput('onDegraded', 'boolean', '性能下降');
    this.addOutput('onCritical', 'boolean', '性能严重');
    this.addOutput('onError', 'boolean', '性能错误');
  }

  public execute(inputs: any): any {
    const action = inputs?.action as string || 'monitor';
    const deviceId = inputs?.deviceId as string;
    const timeRange = inputs?.timeRange as string || 'hour';
    const metrics = inputs?.metrics as string[] || [];
    const benchmark = inputs?.benchmark as any || {};

    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      let result: any;

      switch (action) {
        case 'monitor':
          result = this.monitorPerformance(deviceId, timeRange, metrics);
          break;
        case 'analyze':
          result = this.analyzePerformance(deviceId, timeRange, benchmark);
          break;
        case 'benchmark':
          result = this.benchmarkPerformance(deviceId, benchmark);
          break;
        default:
          throw new Error(`不支持的性能动作: ${action}`);
      }

      return result;
    } catch (error) {
      Debug.error('DevicePerformanceNode', '设备性能操作失败', error);
      return {
        performance: null,
        performanceId: '',
        efficiency: 0,
        utilization: 0,
        throughput: 0,
        trends: [],
        onOptimal: false,
        onDegraded: false,
        onCritical: false,
        onError: true
      };
    }
  }

  private async monitorPerformance(deviceId: string, timeRange: string, metrics: string[]): Promise<any> {
    const performance = deviceManagementManager.monitorPerformance(deviceId, timeRange, metrics);
    const efficiency = performance.efficiency;
    const utilization = performance.utilization;
    const throughput = performance.throughput;

    Debug.log('DevicePerformanceNode', `设备性能监控: ${deviceId} - 效率: ${efficiency}%`);

    return {
      performance,
      performanceId: performance.id,
      efficiency,
      utilization,
      throughput,
      trends: performance.trends,
      onOptimal: efficiency >= 90,
      onDegraded: efficiency >= 70 && efficiency < 90,
      onCritical: efficiency < 70,
      onError: false
    };
  }

  private async analyzePerformance(deviceId: string, timeRange: string, benchmark: any): Promise<any> {
    const analysis = deviceManagementManager.analyzePerformance(deviceId, timeRange, benchmark);
    const efficiency = analysis.efficiency;

    Debug.log('DevicePerformanceNode', `设备性能分析: ${deviceId}`);

    return {
      performance: analysis,
      performanceId: analysis.id,
      efficiency,
      utilization: analysis.utilization,
      throughput: analysis.throughput,
      trends: analysis.trends,
      onOptimal: efficiency >= 90,
      onDegraded: efficiency >= 70 && efficiency < 90,
      onCritical: efficiency < 70,
      onError: false
    };
  }

  private async benchmarkPerformance(deviceId: string, benchmark: any): Promise<any> {
    const comparison = deviceManagementManager.benchmarkPerformance(deviceId, benchmark);
    const efficiency = comparison.currentEfficiency;

    Debug.log('DevicePerformanceNode', `设备性能基准对比: ${deviceId}`);

    return {
      performance: comparison,
      performanceId: comparison.id,
      efficiency,
      utilization: comparison.utilization,
      throughput: comparison.throughput,
      trends: [],
      onOptimal: efficiency >= benchmark.efficiency,
      onDegraded: efficiency < benchmark.efficiency && efficiency >= benchmark.efficiency * 0.8,
      onCritical: efficiency < benchmark.efficiency * 0.8,
      onError: false
    };
  }
}

/**
 * 设备告警节点
 */
export class DeviceAlertNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceAlert';
  public static readonly NAME = '设备告警';
  public static readonly DESCRIPTION = '管理设备告警和通知';

  constructor(nodeType: string = DeviceAlertNode.TYPE, name: string = DeviceAlertNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('action', 'string', '告警动作', 'create');
    this.addInput('deviceId', 'string', '设备ID', '');
    this.addInput('alertType', 'string', '告警类型', 'warning');
    this.addInput('message', 'string', '告警消息', '');
    this.addInput('severity', 'string', '严重程度', 'medium');
    this.addInput('threshold', 'object', '阈值设置', {});
  }

  private setupOutputs(): void {
    this.addOutput('alert', 'object', '告警信息');
    this.addOutput('alertId', 'string', '告警ID');
    this.addOutput('activeAlerts', 'array', '活跃告警');
    this.addOutput('alertCount', 'number', '告警数量');
    this.addOutput('onAlertCreated', 'boolean', '告警创建');
    this.addOutput('onAlertResolved', 'boolean', '告警解决');
    this.addOutput('onAlertEscalated', 'boolean', '告警升级');
    this.addOutput('onError', 'boolean', '告警错误');
  }

  public execute(inputs: any): any {
    const action = inputs?.action as string || 'create';
    const deviceId = inputs?.deviceId as string;
    const alertType = inputs?.alertType as string || 'warning';
    const message = inputs?.message as string;
    const severity = inputs?.severity as string || 'medium';
    const threshold = inputs?.threshold as any || {};

    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      let result: any;

      switch (action) {
        case 'create':
          result = this.createAlert(deviceId, alertType, message, severity);
          break;
        case 'resolve':
          result = this.resolveAlert(deviceId, alertType);
          break;
        case 'escalate':
          result = this.escalateAlert(deviceId, alertType);
          break;
        case 'list':
          result = this.listAlerts(deviceId);
          break;
        case 'check':
          result = this.checkThresholds(deviceId, threshold);
          break;
        default:
          throw new Error(`不支持的告警动作: ${action}`);
      }

      return result;
    } catch (error) {
      Debug.error('DeviceAlertNode', '设备告警操作失败', error);
      return {
        alert: null,
        alertId: '',
        activeAlerts: [],
        alertCount: 0,
        onAlertCreated: false,
        onAlertResolved: false,
        onAlertEscalated: false,
        onError: true
      };
    }
  }

  private async createAlert(deviceId: string, alertType: string, message: string, severity: string): Promise<any> {
    const alert = deviceManagementManager.createAlert({
      deviceId,
      alertType,
      message,
      severity,
      timestamp: new Date()
    });

    const activeAlerts = deviceManagementManager.getActiveAlerts(deviceId);

    Debug.log('DeviceAlertNode', `设备告警创建: ${deviceId} - ${alertType}`);

    return {
      alert,
      alertId: alert.id,
      activeAlerts,
      alertCount: activeAlerts.length,
      onAlertCreated: true,
      onAlertResolved: false,
      onAlertEscalated: false,
      onError: false
    };
  }

  private async resolveAlert(deviceId: string, alertType: string): Promise<any> {
    const success = deviceManagementManager.resolveAlert(deviceId, alertType);
    const activeAlerts = deviceManagementManager.getActiveAlerts(deviceId);

    Debug.log('DeviceAlertNode', `设备告警解决: ${deviceId} - ${alertType}`);

    return {
      alert: null,
      alertId: '',
      activeAlerts,
      alertCount: activeAlerts.length,
      onAlertCreated: false,
      onAlertResolved: success,
      onAlertEscalated: false,
      onError: !success
    };
  }

  private async escalateAlert(deviceId: string, alertType: string): Promise<any> {
    const alert = deviceManagementManager.escalateAlert(deviceId, alertType);
    const activeAlerts = deviceManagementManager.getActiveAlerts(deviceId);

    Debug.log('DeviceAlertNode', `设备告警升级: ${deviceId} - ${alertType}`);

    return {
      alert,
      alertId: alert?.id || '',
      activeAlerts,
      alertCount: activeAlerts.length,
      onAlertCreated: false,
      onAlertResolved: false,
      onAlertEscalated: true,
      onError: false
    };
  }

  private async listAlerts(deviceId: string): Promise<any> {
    const activeAlerts = deviceManagementManager.getActiveAlerts(deviceId);

    Debug.log('DeviceAlertNode', `设备告警列表: ${deviceId} - ${activeAlerts.length}个告警`);

    return {
      alert: null,
      alertId: '',
      activeAlerts,
      alertCount: activeAlerts.length,
      onAlertCreated: false,
      onAlertResolved: false,
      onAlertEscalated: false,
      onError: false
    };
  }

  private async checkThresholds(deviceId: string, threshold: any): Promise<any> {
    const alerts = deviceManagementManager.checkThresholds(deviceId, threshold);

    Debug.log('DeviceAlertNode', `设备阈值检查: ${deviceId} - ${alerts.length}个告警`);

    return {
      alert: null,
      alertId: '',
      activeAlerts: alerts,
      alertCount: alerts.length,
      onAlertCreated: alerts.length > 0,
      onAlertResolved: false,
      onAlertEscalated: false,
      onError: false
    };
  }
}

/**
 * 设备生命周期节点
 */
export class DeviceLifecycleNode extends VisualScriptNode {
  public static readonly TYPE = 'DeviceLifecycle';
  public static readonly NAME = '设备生命周期';
  public static readonly DESCRIPTION = '管理设备的整个生命周期';

  constructor(nodeType: string = DeviceLifecycleNode.TYPE, name: string = DeviceLifecycleNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('action', 'string', '生命周期动作', 'track');
    this.addInput('deviceId', 'string', '设备ID', '');
    this.addInput('stage', 'string', '生命周期阶段', '');
    this.addInput('metadata', 'object', '元数据', {});
    this.addInput('milestone', 'string', '里程碑', '');
  }

  private setupOutputs(): void {
    this.addOutput('lifecycle', 'object', '生命周期数据');
    this.addOutput('lifecycleId', 'string', '生命周期ID');
    this.addOutput('currentStage', 'string', '当前阶段');
    this.addOutput('age', 'number', '设备年龄');
    this.addOutput('remainingLife', 'number', '剩余寿命');
    this.addOutput('milestones', 'array', '里程碑');
    this.addOutput('onInstalled', 'boolean', '设备安装');
    this.addOutput('onCommissioned', 'boolean', '设备投产');
    this.addOutput('onRetired', 'boolean', '设备退役');
    this.addOutput('onMilestone', 'boolean', '里程碑达成');
    this.addOutput('onError', 'boolean', '生命周期错误');
  }

  public execute(inputs: any): any {
    const action = inputs?.action as string || 'track';
    const deviceId = inputs?.deviceId as string;
    const stage = inputs?.stage as string;
    const metadata = inputs?.metadata as any || {};
    const milestone = inputs?.milestone as string;

    if (!deviceId) {
      throw new Error('设备ID不能为空');
    }

    try {
      let result: any;

      switch (action) {
        case 'track':
          result = this.trackLifecycle(deviceId);
          break;
        case 'update_stage':
          result = this.updateStage(deviceId, stage, metadata);
          break;
        case 'add_milestone':
          result = this.addMilestone(deviceId, milestone, metadata);
          break;
        case 'calculate_age':
          result = this.calculateAge(deviceId);
          break;
        case 'predict_life':
          result = this.predictRemainingLife(deviceId);
          break;
        default:
          throw new Error(`不支持的生命周期动作: ${action}`);
      }

      return result;
    } catch (error) {
      Debug.error('DeviceLifecycleNode', '设备生命周期操作失败', error);
      return {
        lifecycle: null,
        lifecycleId: '',
        currentStage: '',
        age: 0,
        remainingLife: 0,
        milestones: [],
        onInstalled: false,
        onCommissioned: false,
        onRetired: false,
        onMilestone: false,
        onError: true
      };
    }
  }

  private async trackLifecycle(deviceId: string): Promise<any> {
    const lifecycle = deviceManagementManager.trackLifecycle(deviceId);
    const currentStage = lifecycle.currentStage;
    const age = lifecycle.age;
    const remainingLife = lifecycle.remainingLife;

    Debug.log('DeviceLifecycleNode', `设备生命周期跟踪: ${deviceId} - 阶段: ${currentStage}`);

    return {
      lifecycle,
      lifecycleId: lifecycle.id,
      currentStage,
      age,
      remainingLife,
      milestones: lifecycle.milestones,
      onInstalled: currentStage === 'installed',
      onCommissioned: currentStage === 'commissioned',
      onRetired: currentStage === 'retired',
      onMilestone: false,
      onError: false
    };
  }

  private async updateStage(deviceId: string, stage: string, metadata: any): Promise<any> {
    const lifecycle = deviceManagementManager.updateLifecycleStage(deviceId, stage, metadata);
    const currentStage = lifecycle.currentStage;

    Debug.log('DeviceLifecycleNode', `设备生命周期阶段更新: ${deviceId} - ${stage}`);

    return {
      lifecycle,
      lifecycleId: lifecycle.id,
      currentStage,
      age: lifecycle.age,
      remainingLife: lifecycle.remainingLife,
      milestones: lifecycle.milestones,
      onInstalled: stage === 'installed',
      onCommissioned: stage === 'commissioned',
      onRetired: stage === 'retired',
      onMilestone: false,
      onError: false
    };
  }

  private async addMilestone(deviceId: string, milestone: string, metadata: any): Promise<any> {
    const lifecycle = deviceManagementManager.addLifecycleMilestone(deviceId, milestone, metadata);

    Debug.log('DeviceLifecycleNode', `设备生命周期里程碑: ${deviceId} - ${milestone}`);

    return {
      lifecycle,
      lifecycleId: lifecycle.id,
      currentStage: lifecycle.currentStage,
      age: lifecycle.age,
      remainingLife: lifecycle.remainingLife,
      milestones: lifecycle.milestones,
      onInstalled: false,
      onCommissioned: false,
      onRetired: false,
      onMilestone: true,
      onError: false
    };
  }

  private async calculateAge(deviceId: string): Promise<any> {
    const lifecycle = deviceManagementManager.calculateDeviceAge(deviceId);
    const age = lifecycle.age;

    Debug.log('DeviceLifecycleNode', `设备年龄计算: ${deviceId} - ${age}天`);

    return {
      lifecycle,
      lifecycleId: lifecycle.id,
      currentStage: lifecycle.currentStage,
      age,
      remainingLife: lifecycle.remainingLife,
      milestones: lifecycle.milestones,
      onInstalled: false,
      onCommissioned: false,
      onRetired: false,
      onMilestone: false,
      onError: false
    };
  }

  private async predictRemainingLife(deviceId: string): Promise<any> {
    const prediction = deviceManagementManager.predictRemainingLife(deviceId);
    const remainingLife = prediction.remainingLife;

    Debug.log('DeviceLifecycleNode', `设备剩余寿命预测: ${deviceId} - ${remainingLife}天`);

    return {
      lifecycle: prediction,
      lifecycleId: prediction.id,
      currentStage: prediction.currentStage,
      age: prediction.age,
      remainingLife,
      milestones: prediction.milestones,
      onInstalled: false,
      onCommissioned: false,
      onRetired: remainingLife <= 0,
      onMilestone: false,
      onError: false
    };
  }
}
